## 进出条件类
from backtrader import (
    Indicator,
    MetaLineIterator,
    LineIterator,
    IndicatorBase,
)
from .utils.date import freq2dayperiod


class MetaCondition(MetaLineIterator):
    """
    策略的进入退出条件元类
    自动载入策略的broker和datas
    """

    def dopreinit(cls, obj, *args, **kwargs):
        _obj, args, kwargs = super().dopreinit(obj, *args, **kwargs)
        # 创建新实例
        instance = obj

        # 从 _owner 添加指定属性到新实例
        if instance._owner is not None:
            for attr in getattr(obj, "_attrs_to_add", []):
                if hasattr(instance._owner, attr):
                    setattr(instance, attr, getattr(instance._owner, attr))

        return instance, args, kwargs

    def dopostinit(cls, _obj, *args, **kwargs):
        super().dopostinit(_obj, *args, **kwargs)
        instance = _obj

        # extra_log_args
        instance._extra_args = {}

        # long, short transfer to num
        if isinstance(instance.p.side, str):
            if instance.p.side.upper() == "LONG":
                instance.p.side = cls.LONG
            elif instance.p.side.upper() == "SHORT":
                instance.p.side = cls.SHORT
            else:
                raise ValueError("param side invalid")

        # 将指标类型的属性存入_indicators中
        instance._indicator_dict = {}
        for attr, value in instance.__dict__.items():
            if isinstance(value, Indicator):
                instance._indicator_dict[attr] = value

        # 添加一个实时数据计算开关
        instance._live_started = False

        return instance, args, kwargs


class Condition(IndicatorBase, metaclass=MetaCondition):
    _attrs_to_add = ["broker", "logger"]

    LONG, SHORT = 1, -1

    alias = ("Condition",)

    params = (
        ("side", LONG),
        ("simulate", False),
    )

    lines = ("value",)

    plotinfo = dict(
        b_plotname="",  # 指标做图名称, 指定名称的才会保存
        b_plot=True,  # 是否展示图
        b_subplot=True,  # 是否绘制在附图中
        b_plotabove=False,  # 是否绘制在主图上方
    )

    plotline = dict(
        value=dict(
            b_marker="line",  # 图标形状
            b_size=1.0,  # 图标大小
            b_color="navy",  # 图标颜色
            b_digit=0,  # 默认精度
        )
    )

    def trigger_update_status(self):
        return self.update_status()

    def update_status(self):
        # 刷新状态量
        pass

    def nextstart(self):
        self.trigger_next()

    def prenext(self):
        pass

    def trigger_next(self):
        return self.next()

    def next(self):
        return False

    def get_indicator_dict(self):
        return self._indicator_dict

    def _next(self):
        clock_len = self._clk_update()

        for indicator in self._lineiterators[LineIterator.IndType]:
            indicator._next()

        self._notify()
        # assume indicators and others operate on same length datas
        # although the above operation can be generalized
        # 模拟条件下遵循一般规则
        if self.p.simulate:
            if clock_len > self._minperiod:
                # 刷新状态量
                self.trigger_update_status()
                # 判断触发条件
                res = self.trigger_next()
            elif clock_len == self._minperiod:
                # 刷新状态量
                self.trigger_update_status()
                res = self.nextstart()
            elif clock_len:
                res = self.prenext()

        # 实际交易需要等待实时数据
        elif self._owner.data.is_live_status():
            if clock_len >= self._minperiod:
                if self._live_started == True:
                    # 刷新状态量
                    self.trigger_update_status()
                    # 判断触发条件
                    res = self.trigger_next()
                else:
                    self._live_started = True
                    # 刷新状态量
                    self.trigger_update_status()
                    res = self.nextstart()
            else:
                res = self.prenext()
        else:
            res = 0

        if res == None:
            res = 0

        self.lines.value[0] = float(res)

    @classmethod
    def get_name(cls):
        if cls.alias:
            return cls.alias[0]
        return cls.__name__

    def update_extra_args(self, **kwargs):
        self._extra_args.update(kwargs)

    def get_extra_args(self, key):
        return self._extra_args.get(key, None)

    def get_plotinfo(cls):
        return {}

    @property
    def day_period(self):
        return freq2dayperiod(self.data.p.freq)

    def __repr__(self):
        string = self.get_name()
        return f"Condition({string})"
